# -*- coding: utf-8 -*-
import os
import uuid
from flask import Flask, request, send_file, Response
import shutil

import pypandoc


app = Flask(__name__)
backup_dir = './bk-eqy-md-doc'
if not os.path.exists(backup_dir):
    os.makedirs(backup_dir)


def save_md_and_convert_to_word(markdown_text, md_file_path, word_file_path):
    # 保存 Markdown 文本到 .md 文件
    with open(md_file_path, 'w') as file:
        file.write(markdown_text.encode('utf-8'))

    # 将 .md 文件转换为 Word 文件
    output = pypandoc.convert_file(md_file_path, 'docx', outputfile=word_file_path)
    assert output == ""


@app.route('/convert', methods=['POST'])
def convert():
    # 获取输入的 Markdown 文本
    markdown_text = request.form.get('markdown_text')
    if not markdown_text:
        return "Missing 'markdown_text' in the request.", 400

    # 生成唯一的文件名
    unique_id = str(uuid.uuid4())
    md_file_path = '%s.md' % unique_id
    word_file_path = '%s.docx' % unique_id

    # 保存 Markdown 文本并转换为 Word 文件
    save_md_and_convert_to_word(markdown_text, md_file_path, word_file_path)

    # 返回下载链接
    download_url = '%sdownload/%s' % (request.host_url, word_file_path)
    return {'download_url': download_url}


@app.route('/download/<path:filename>', methods=['GET'])
def download(filename):
    if os.path.exists(filename):
        # 读取文件内容
        with open(filename, 'rb') as file:
            file_content = file.read()

        # 移动文件到备份目录
        md_filename = filename.replace('.docx', '.md')
        if os.path.exists(md_filename):
            shutil.move(md_filename, os.path.join(backup_dir, md_filename))
        shutil.move(filename, os.path.join(backup_dir, filename))

        # 发送文件内容
        headers = {
            'Content-Disposition': 'attachment; filename=%s' % filename
        }
        return Response(file_content, headers=headers)
    else:
        return "File not found.", 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8280)


